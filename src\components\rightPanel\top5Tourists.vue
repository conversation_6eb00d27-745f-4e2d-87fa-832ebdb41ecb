<!-- 接待游客人数TOP5 -->
<template>
  <CPanel>
    <template #header>光伏系统</template>
    <template #content>
      <div class="photovoltaic-system">
        <!-- AC视在/无功功率对比 -->
        <div class="power-comparison">
          <div class="section-title">AC视在/无功功率对比</div>
          <div class="power-display">
            <div class="power-item">
              <div class="power-value">{{ displayData.apparent }}<span class="unit">kwh</span></div>
              <div class="power-label">AC视在功率</div>
            </div>
            <div class="center-circle">
              <div class="circle-bg"></div>
              <CEcharts ref="circleChartRef" :option="circleOption" />
              <div class="circle-content">
                <div class="circle-value">{{ displayData.total }}</div>
                <div class="circle-label">总发电量</div>
              </div>
            </div>
            <div class="power-item">
              <div class="power-value">{{ displayData.reactive }}<span class="unit">kwh</span></div>
              <div class="power-label">AC无功功率</div>
            </div>
          </div>
        </div>

        <!-- 发电量统计 -->
        <div class="power-statistics">
          <div class="section-title">发电量统计</div>
          <div class="chart-container">
            <CEcharts ref="barChartRef" :option="barOption" />
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import { getPhotovoltaicData, type PhotovoltaicData } from '@/api'

const circleChartRef = ref()
const barChartRef = ref()
const circleOption = ref<any>({})
const barOption = ref<any>({})
const currentDataIndex = ref(0) // 当前显示数据的起始索引
const photovoltaicData = ref<any>({
  data: {
    code: 0,
    msg: '',
    ok: false,
    data: {
      total: '0',
      apparent: '0',
      reactive: '0',
      electricity: {
        type: [],
        axis: [],
        data: []
      }
    }
  }
})

// 计算属性确保数据正确显示
const displayData = computed(() => {
  console.log('计算属性触发，当前数据:', photovoltaicData.value)
  
  // 打印三个圈起来的值
  const total = photovoltaicData.value.data?.data?.total || '0'
  const apparent = photovoltaicData.value.data?.data?.apparent || '0'
  const reactive = photovoltaicData.value.data?.data?.reactive || '0'
  
  console.log('圈起来的三个值:')
  console.log('total:', total)
  console.log('apparent:', apparent)
  console.log('reactive:', reactive)
  
  return {
    total,
    apparent,
    reactive
  }
})

// 获取光伏系统数据
const fetchPhotovoltaicData = async () => {
  try {
    console.log('开始获取光伏系统数据...')
    const response = await getPhotovoltaicData()
    console.log('API响应:', response)

    // 检查响应结构
    if (response && response.data) {
      console.log('设置数据:', response.data)
      photovoltaicData.value = response
      // 更新图表
      updateCharts()
    } else {
      console.log('API响应无效:', response)
    }
  } catch (error) {
    console.error('获取光伏系统数据失败:', error)
  }
}

// 更新图表
const updateCharts = () => {
  console.log('更新图表，当前数据:', photovoltaicData.value)
  if (circleChartRef.value) {
    circleOption.value = createCircleChart()
  }
  if (barChartRef.value) {
    barOption.value = createBarChart()
  }
}

// 创建圆环图配置
const createCircleChart = () => {
  const total = parseFloat(photovoltaicData.value.data?.data?.total) || 0
  const generated = total
  const remaining = Math.max(0, 100 - generated) // 假设总量为100

  return {
    series: [
      {
        type: 'pie',
        radius: ['75%', '85%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: generated,
            name: '已发电量',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00FFFF' },
                { offset: 0.5, color: '#00CED1' },
                { offset: 1, color: '#20B2AA' }
              ])
            }
          },
          {
            value: remaining,
            name: '剩余',
            itemStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        ],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        animation: true,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: 0
      }
    ]
  }
}

// 创建柱状图配置
const createBarChart = () => {
  const axis = photovoltaicData.value.data?.data?.electricity?.axis || []
  const data = photovoltaicData.value.data?.data?.electricity?.data?.[0] || []

  // 动态显示8个数据点，根据currentDataIndex循环显示
  const totalLength = axis.length
  let displayAxis: any[]
  let displayData: any[]

  if (totalLength <= 8) {
    // 如果总数据不超过8个，直接显示全部
    displayAxis = axis
    displayData = data
  } else {
    // 如果数据超过8个，循环显示
    const startIndex = currentDataIndex.value
    const endIndex = startIndex + 8

    if (endIndex <= totalLength) {
      // 正常切片
      displayAxis = axis.slice(startIndex, endIndex)
      displayData = data.slice(startIndex, endIndex)
    } else {
      // 需要循环，从头开始补充
      displayAxis = [...axis.slice(startIndex), ...axis.slice(0, endIndex - totalLength)]
      displayData = [...data.slice(startIndex), ...data.slice(0, endIndex - totalLength)]
    }
  }

  return {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '5%',
      right: '2%',
      bottom: '14%',
    },
    tooltip: {
      show: false,
    },
    xAxis: {
      data: displayAxis,
      axisLine: {
        lineStyle: {
          color: 'transparent', //底部边框颜色
        },
      },
      axisLabel: {
        textStyle: {
          color: '#fff', //底部文字颜色
          fontSize: 12,
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.2)', //网格线的颜色
            width: 1,
            type: 'solid',
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'transparent', //左边框颜色
          },
        },
        axisLabel: {
          show: true,
          fontSize: 12,
          textStyle: {
            color: '#ADD6FF', //左文字颜色
          },
        },
      },
    ],
    series: [
      {
        name: '发电量',
        type: 'bar',
        barWidth: 18,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(112,241,245,0.1)',
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#70F1F5', //渐变1
              },
              {
                offset: 1,
                color: '#70F1F5', //渐变2
              },
            ]),
          },
        },
        data: displayData.map((d: any) => parseFloat(d) || 0),
        z: 0,
        zlevel: 0,
      },
      {
        type: 'pictorialBar',
        barWidth: 18,
        itemStyle: {
          normal: {
            color: 'rgba(0,63,119,1)', //数据的间隔颜色
          },
        },
        symbolRepeat: 'fixed',
        symbolMargin: 3,
        symbol: 'rect',
        symbolSize: [18, 4],
        symbolPosition: 'end',
        symbolOffset: [0, 0],
        data: displayData.map((d: any) => parseFloat(d) || 0),
        z: 1,
        zlevel: 0,
      },
      {
        type: 'pictorialBar',
        barWidth: 18,
        itemStyle: {
          normal: {
            color: 'transparent', //数据的间隔颜色
          },
        },
        symbolMargin: 3,
        symbol: 'rect',
        symbolSize: [18, 4],
        data: displayData.map((d: any) => parseFloat(d) || 0),
        z: 1,
        zlevel: 0,
        label: {
          show: true,
          position: 'top',
          fontSize: 14,
          color: '#fff', //柱状顶部文字颜色
          formatter: function (params: any) {
            return params.value + '%';
          },
        },
      },
    ],
  }
}

// 数据轮播功能
const startDataRotation = () => {
  const axis = photovoltaicData.value.data?.data?.electricity?.axis || []
  if (axis.length > 8) {
    setInterval(() => {
      currentDataIndex.value = (currentDataIndex.value + 1) % axis.length
      if (barChartRef.value) {
        barOption.value = createBarChart()
      }
    }, 3000) // 每3秒切换一次
  }
}

onMounted(async () => {
  // 获取数据并初始化图表
  await fetchPhotovoltaicData()

  // 启动数据轮播
  startDataRotation()

  // 设置定时刷新（每30秒刷新一次）
  setInterval(fetchPhotovoltaicData, 5000)
})
</script>
<style lang="scss" scoped>
.photovoltaic-system {
  width: 472px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: url('@/assets/img/光伏系统底框.png') no-repeat center center;
  background-size: 100% 100%;

  .main-title {
    position: relative;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    background: url('@/assets/img/small.png') no-repeat center center;
    background-size: 100% 100%;
    font-size: 20px;
    letter-spacing: 1px;
    color: #fff;
    padding-left: 20px;
  }

  .power-comparison {
    position: relative;
    // margin-bottom: 8px;

    .section-title {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      background: url('@/assets/img/n_samllTitle.png') no-repeat center center;
      background-size: 100% 100%;
      font-size: 16px;
      color: #fff;
      padding-left: 20px;
      margin-bottom: 8px;
    }

    .power-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      background: url('@/assets/img/gf_bg.png') no-repeat center center;
      background-size: 100% 100%;

      .power-item {
        text-align: center;
        color: #fff;

        .power-value {
          font-size: 20px;
          font-weight: bold;
          color: #00FFFF;
          margin-bottom: 4px;

          .unit {
            font-size: 14px;
            color: #C5D6E6;
          }
        }

        .power-label {
          font-size: 14px;
          color: #C5D6E6;
        }
      }

      .center-circle {
        width: 140px;
        height: 140px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .circle-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          // // background: url('@/assets/img/circle_bg.svg') no-repeat center center;
          // background-size: contain;
          // z-index: 1;
        }

        .circle-content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          z-index: 3;
          color: #fff;

          .circle-value {
            font-size: 24px;
            font-weight: bold;
            color: #00FFFF;
            margin-bottom: 2px;
          }

          .circle-label {
            font-size: 14px;
            color: #C5D6E6;
          }
        }

        :deep(.echarts) {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 2;
        }
      }
    }
  }



  .power-statistics {
    flex: 1;

    .section-title {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      background: url('@/assets/img/n_samllTitle.png') no-repeat center center;
      background-size: 100% 100%;
      font-size: 16px;
      color: #fff;
      padding-left: 20px;
      margin-bottom: 8px;
    }

    .chart-container {
      height: 120px;
      width: 100%;
    }
  }
}
</style>
